# SwarmX Hooks

SwarmX supports lifecycle hooks that allow you to execute MCP tools at specific points during agent execution. This enables powerful capabilities like logging, metrics collection, debugging, and custom workflow orchestration.

## Hook Overview

A `Hook` is a Pydantic BaseModel that defines which MCP tools to execute at various lifecycle events:

- `on_start`: Executed when the agent begins processing
- `on_end`: Executed when the agent finishes processing  
- `on_tool_start`: Executed before any tool call
- `on_tool_end`: Executed after any tool call
- `on_subagents_start`: Executed before subagent processing begins
- `on_subagents_end`: Executed after subagent processing ends

## Creating Hooks

```python
from swarmx import Hook

# Create a logging hook
logging_hook = Hook(
    on_start="log_agent_start",
    on_end="log_agent_end",
    on_tool_start="log_tool_start", 
    on_tool_end="log_tool_end"
)

# Create a metrics hook
metrics_hook = Hook(
    on_start="start_timer",
    on_end="record_execution_time"
)
```

## Adding Hooks to Agents

```python
from swarmx import Agent, Hook

# Create hooks
hook1 = Hook(on_start="initialize_session")
hook2 = Hook(on_end="cleanup_session")

# Add hooks to agent
agent = Agent(
    name="MyAgent",
    hooks=[hook1, hook2]
)
```

## Hook Execution

Hooks are executed automatically during agent lifecycle:

1. **on_start** - Called at the beginning of `_run()` and `_run_stream()`
2. **on_tool_start** - Called before executing any tool calls
3. **on_tool_end** - Called after executing tool calls
4. **on_subagents_start** - Called before running subagents
5. **on_subagents_end** - Called after running subagents  
6. **on_end** - Called at the end of processing

## Hook Tool Requirements

Hook tools must be available in your MCP server configuration. The tools receive the agent's context as input parameters.

Example MCP tool for logging:

```python
# In your MCP server
@server.call_tool()
async def log_agent_start(context: dict) -> str:
    """Log when an agent starts processing."""
    logger.info(f"Agent started with context: {context}")
    return "Logged agent start"
```

## Error Handling

If a hook tool fails, the error is logged but does not stop agent execution. This ensures that hook failures don't break your main workflow.

## Serialization

Hooks are fully serializable since they only store tool names (strings) rather than function references:

```python
# Serialize
hook_dict = hook.model_dump()

# Deserialize  
restored_hook = Hook.model_validate(hook_dict)
```

## Use Cases

- **Logging**: Track agent execution flow
- **Metrics**: Measure performance and usage
- **Debugging**: Inspect agent state at key points
- **Auditing**: Record all agent actions
- **Integration**: Trigger external systems
- **Workflow**: Coordinate complex multi-agent processes

## Example

See `examples/hooks_example.py` for a complete working example.
