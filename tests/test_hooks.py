"""Tests for Hook functionality."""

from unittest.mock import AsyncMock, patch

import pytest

from swarmx import Agent, Hook
from swarmx.mcp_client import CLIENT_REGISTRY

pytestmark = pytest.mark.anyio


@pytest.fixture
def mock_client_registry():
    """Mock the CLIENT_REGISTRY for testing."""
    with patch.object(CLIENT_REGISTRY, "call_tool", new_callable=AsyncMock) as mock:
        yield mock


async def test_hook_creation():
    """Test that Hook can be created with all fields."""
    hook = Hook(
        on_start="start_tool",
        on_end="end_tool",
        on_tool_start="tool_start_tool",
        on_tool_end="tool_end_tool",
        on_subagents_start="subagents_start_tool",
        on_subagents_end="subagents_end_tool",
    )

    assert hook.on_start == "start_tool"
    assert hook.on_end == "end_tool"
    assert hook.on_tool_start == "tool_start_tool"
    assert hook.on_tool_end == "tool_end_tool"
    assert hook.on_subagents_start == "subagents_start_tool"
    assert hook.on_subagents_end == "subagents_end_tool"


async def test_hook_serialization():
    """Test that Hook can be serialized and deserialized."""
    hook = Hook(
        on_start="start_tool",
        on_end="end_tool",
    )

    # Test serialization
    hook_dict = hook.model_dump()
    expected = {
        "on_start": "start_tool",
        "on_end": "end_tool",
        "on_tool_start": None,
        "on_tool_end": None,
        "on_subagents_start": None,
        "on_subagents_end": None,
    }
    assert hook_dict == expected

    # Test deserialization
    hook_restored = Hook.model_validate(hook_dict)
    assert hook_restored.on_start == "start_tool"
    assert hook_restored.on_end == "end_tool"
    assert hook_restored.on_tool_start is None


async def test_agent_with_hooks():
    """Test that Agent can be created with hooks."""
    hook = Hook(on_start="start_tool")
    agent = Agent(hooks=[hook])

    assert len(agent.hooks) == 1
    assert agent.hooks[0].on_start == "start_tool"


async def test_execute_hooks_with_valid_tool(mock_client_registry):
    """Test that _execute_hooks calls the correct tool."""
    hook = Hook(on_start="test_tool")
    agent = Agent(hooks=[hook])

    await agent._execute_hooks("on_start", {"test": "context"})

    mock_client_registry.assert_called_once_with("test_tool", {"test": "context"})


async def test_execute_hooks_with_none_tool(mock_client_registry):
    """Test that _execute_hooks skips None tools."""
    hook = Hook(on_start=None, on_end="end_tool")
    agent = Agent(hooks=[hook])

    await agent._execute_hooks("on_start", {"test": "context"})

    mock_client_registry.assert_not_called()


async def test_execute_hooks_with_multiple_hooks(mock_client_registry):
    """Test that _execute_hooks calls all hooks of the same type."""
    hook1 = Hook(on_start="tool1")
    hook2 = Hook(on_start="tool2")
    agent = Agent(hooks=[hook1, hook2])

    await agent._execute_hooks("on_start", {"test": "context"})

    assert mock_client_registry.call_count == 2
    mock_client_registry.assert_any_call("tool1", {"test": "context"})
    mock_client_registry.assert_any_call("tool2", {"test": "context"})


async def test_execute_hooks_with_exception(mock_client_registry):
    """Test that _execute_hooks handles exceptions gracefully."""
    mock_client_registry.side_effect = Exception("Tool failed")

    hook = Hook(on_start="failing_tool")
    agent = Agent(hooks=[hook])

    # Should not raise an exception
    await agent._execute_hooks("on_start", {"test": "context"})

    mock_client_registry.assert_called_once_with("failing_tool", {"test": "context"})


async def test_execute_hooks_with_basemodel_context(mock_client_registry):
    """Test that _execute_hooks properly handles BaseModel context."""
    from pydantic import BaseModel

    class TestContext(BaseModel):
        value: str

    hook = Hook(on_start="test_tool")
    agent = Agent(hooks=[hook])
    context = TestContext(value="test")

    await agent._execute_hooks("on_start", context)

    mock_client_registry.assert_called_once_with("test_tool", {"value": "test"})


async def test_hook_integration_with_agent_run(mock_client_registry):
    """Test that hooks are executed during agent run."""
    hook = Hook(on_start="start_tool", on_end="end_tool")
    agent = Agent(hooks=[hook])

    await agent._run(messages=[{"role": "user", "content": "test"}])

    # Verify hooks were called
    assert mock_client_registry.call_count >= 2
    mock_client_registry.assert_any_call("start_tool", {})
    mock_client_registry.assert_any_call("end_tool", {})


def test_hook_json_schema():
    """Test that Hook can be included in JSON schema generation."""
    hook = Hook(on_start="test_tool")
    schema = hook.model_json_schema()

    assert "properties" in schema
    assert "on_start" in schema["properties"]
    assert "on_end" in schema["properties"]
    assert "on_tool_start" in schema["properties"]
    assert "on_tool_end" in schema["properties"]
    assert "on_subagents_start" in schema["properties"]
    assert "on_subagents_end" in schema["properties"]
